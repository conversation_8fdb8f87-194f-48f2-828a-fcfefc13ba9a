# Chronos Platform - Landing Page

![Chronos Platform](logo.png)

## Sobre o Projeto

A Chronos Platform é uma plataforma de sorteios transparente e segura, utilizando tecnologia blockchain para garantir a integridade e confiabilidade de cada sorteio.

Esta landing page foi desenvolvida como demonstração do produto final, exibindo todas as funcionalidades e benefícios da plataforma.

## Tecnologias Utilizadas

- HTML5
- CSS3 com variáveis e design responsivo
- JavaScript puro (Vanilla JS)
- Animações e transições avançadas
- Compatibilidade com todos os navegadores modernos

## Recursos e Funcionalidades

- **Design responsivo**: Adaptável a todos os tamanhos de tela
- **Animações suaves**: Utiliza técnicas de Intersection Observer para carregamento lazy
- **Contador numérico**: Estatísticas animadas
- **Slider de depoimentos**: Exibe feedback de usuários
- **Modal interativo**: Para login e cadastro
- **Timer de contagem regressiva**: Para sorteios ativos
- **Cookies consent**: Gerenciamento de permissões LGPD/GDPR
- **Partículas interativas**: Efeitos visuais no hero
- **Tooltips**: Informações contextuais
- **Toast notifications**: Feedback de ações
- **Preloader**: Melhor experiência de carregamento inicial

## Estrutura do Projeto

```
chronos-platform/
│
├── index.html            # Documento HTML principal
├── styles.css            # Estilos CSS
├── script.js             # Lógica JavaScript
├── logo.png              # Logo da Chronos Platform
├── favicon.ico           # Ícone do site
└── README.md             # Este arquivo
```

## Como Executar

Para executar o projeto localmente, basta abrir o arquivo `index.html` em um navegador web.

Para desenvolvimento:

1. Clone o repositório
2. Abra o arquivo index.html no navegador
3. Para editar o código, use seu editor preferido (VS Code recomendado)

## Recursos Futuros

- Integração com API de blockchain para verificação em tempo real
- Sistema de autenticação completo
- Página de histórico de sorteios
- Área do usuário
- Sistema de pagamento

## Licença

Este projeto é de propriedade da Chronos Platform e não pode ser reproduzido sem autorização.

## Contato

Para mais informações, entre em contato através de:

- **Email**: <EMAIL>
- **Telefone**: (XX) XXXX-XXXX 