/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Cores Chronos */
    --chronos-maroon: #7B1818;
    --ouro-antigo: #D4AF37;
    --violeta-profundo: #6A489F;
    --marmore-ivory: #F5F3EE;
    --carvao-noturno: #2F2F2F;
    --bronze-suave: #8B6F47;
    --ouro-claro: #F2D472;
    --ouro-escuro: #C79B2F;
    --vermelho-erro: #E74C3C;
    --verde-sucesso: #2ECC71;

    /* Gradientes */
    --gradient-hero: linear-gradient(135deg, var(--carvao-noturno) 0%, #1a1a1a 100%);
    --gradient-gold: linear-gradient(135deg, var(--ouro-antigo) 0%, #f4d03f 100%);
    --gradient-card: linear-gradient(145deg, rgba(47, 47, 47, 0.9) 0%, rgba(123, 24, 24, 0.1) 100%);
    --gradient-maroon: linear-gradient(135deg, var(--chronos-maroon) 0%, #9b2323 100%);
    --gradient-glass: linear-gradient(to right bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

    /* Tipografia */
    --font-primary: 'Raleway', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-titles: 'Cinzel', serif;

    /* Sombras */
    --shadow-card: 0 10px 30px rgba(0, 0, 0, 0.3);
    --shadow-button: 0 4px 15px rgba(212, 175, 55, 0.3);
    --shadow-hero: 0 20px 60px rgba(0, 0, 0, 0.5);
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 10px 40px rgba(0, 0, 0, 0.5);
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 20px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index layers */
    --z-back: -1;
    --z-normal: 1;
    --z-front: 10;
    --z-modal: 100;
    --z-toast: 200;
    --z-tooltip: 300;
    --z-header: 1000;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--marmore-ivory);
    background: var(--carvao-noturno);
    overflow-x: hidden;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
}

/* Preloader */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--carvao-noturno);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-toast);
    transition: opacity 0.6s ease, visibility 0.6s ease;
}

.preloader.hide {
    opacity: 0;
    visibility: hidden;
}

.loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
}

.loader-logo {
    width: 80px;
    height: 80px;
    animation: pulse 1.5s ease-in-out infinite alternate;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(212, 175, 55, 0.2);
    border-left-color: var(--ouro-antigo);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    from {
        transform: scale(0.9);
        opacity: 0.8;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Header */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(245, 243, 238, 0.95);
    backdrop-filter: blur(10px);
    z-index: var(--z-header);
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header.scrolled {
    background: rgba(47, 47, 47, 0.95);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.header.scrolled .nav-link {
    color: var(--marmore-ivory);
}

.header.scrolled .nav-link:hover {
    color: var(--ouro-antigo);
}

.header.scrolled .logo-text {
    color: var(--ouro-antigo);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.nav-logo a {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
}

.logo {
    height: 40px;
    width: auto;
    transition: var(--transition-normal);
}

.logo:hover {
    transform: rotate(10deg);
}

.logo-text {
    font-family: var(--font-titles);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--chronos-maroon);
    letter-spacing: 3px;
    transition: var(--transition-normal);
    text-transform: uppercase;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--carvao-noturno);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background: var(--chronos-maroon);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link:focus::after {
    width: 100%;
}

.nav-link:hover {
    color: var(--chronos-maroon);
}

.btn-login {
    background: transparent;
    border: 2px solid var(--chronos-maroon);
    color: var(--chronos-maroon);
    padding: 8px 20px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-login:hover {
    background: var(--chronos-maroon);
    color: white;
    transform: translateY(-2px);
}

.btn-primary {
    background: var(--gradient-gold);
    border: none;
    color: var(--carvao-noturno);
    padding: 10px 24px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.mobile-menu-toggle {
    display: none;
    color: var(--carvao-noturno);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition-normal);
}

.header.scrolled .mobile-menu-toggle {
    color: var(--marmore-ivory);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    box-shadow: var(--shadow-button);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 50;
    border: none;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: var(--gradient-hero);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: var(--z-back);
}

.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-back);
}

.hero-statue {
    position: absolute;
    width: 300px;
    height: 400px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50 10 L60 30 L50 50 L40 30 Z" fill="%23D4AF37" opacity="0.3"/></svg>') no-repeat center;
    background-size: contain;
    transition: all 5s ease-in-out;
}

.left-statue {
    left: -100px;
    top: 20%;
    transform: rotate(-10deg);
    animation: floatLeft 8s ease-in-out infinite alternate;
}

.right-statue {
    right: -100px;
    top: 30%;
    transform: rotate(10deg);
    animation: floatRight 10s ease-in-out infinite alternate;
}

@keyframes floatLeft {
    0% {
        transform: rotate(-10deg) translateY(0);
    }
    100% {
        transform: rotate(-8deg) translateY(-20px);
    }
}

@keyframes floatRight {
    0% {
        transform: rotate(10deg) translateY(0);
    }
    100% {
        transform: rotate(8deg) translateY(-25px);
    }
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
    z-index: var(--z-normal);
    position: relative;
}

.hero-content {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 1s ease, transform 1s ease;
}

.hero-content.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.hero-title {
    margin-bottom: 2rem;
}

.hero-title-main {
    display: block;
    font-family: var(--font-titles);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 8px;
    text-shadow: 0 5px 20px rgba(0, 0, 0, 0.3), 0 2px 5px rgba(212, 175, 55, 0.2);
    opacity: 0;
    text-transform: uppercase;
}

.hero-title-sub {
    display: block;
    font-family: var(--font-titles);
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 500;
    color: var(--marmore-ivory);
    letter-spacing: 10px;
    margin-top: 0.8rem;
    animation: fadeInUp 1s ease 0.5s forwards;
    opacity: 0;
    text-transform: uppercase;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-description {
    max-width: 700px;
    margin: 0 auto 3rem;
    font-size: clamp(1rem, 2vw, 1.25rem);
    color: rgba(245, 243, 238, 0.9);
    animation: fadeIn 1s ease 0.8s forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.stat-card {
    background: var(--gradient-card);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    width: 200px;
    text-align: center;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-card);
    transition: all 0.4s ease;
    transform: translateY(30px);
    opacity: 0;
}

.stat-card.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    border-color: rgba(212, 175, 55, 0.3);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    margin-bottom: 0.5rem;
    background: linear-gradient(to right, var(--ouro-antigo), #f4d03f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.9rem;
    color: rgba(245, 243, 238, 0.8);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2rem;
    animation: fadeIn 1s ease 1.2s forwards;
    opacity: 0;
}

.btn-hero-primary {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    border: none;
    padding: 15px 30px;
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
}

.btn-hero-primary i {
    font-size: 1rem;
}

.btn-hero-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.5);
}

.btn-hero-secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--marmore-ivory);
    padding: 15px 30px;
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.btn-hero-secondary i {
    font-size: 1rem;
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    color: var(--marmore-ivory);
    cursor: pointer;
    animation: bounce 2s infinite;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.hero-scroll-indicator:hover {
    opacity: 1;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-15px);
    }
    60% {
        transform: translateX(-50%) translateY(-7px);
    }
}

/* Current Sweepstakes */
.current-sweepstakes {
    padding: 5rem 0;
    background: var(--carvao-noturno);
    position: relative;
    overflow: hidden;
}

.sweepstakes-card {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-card);
    max-width: 800px;
    margin: 0 auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.5s ease, box-shadow 0.5s ease;
    transform: translateY(20px);
    opacity: 0;
}

.sweepstakes-card.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.sweepstakes-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
}

.sweepstakes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sweepstakes-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--marmore-ivory);
    margin: 0;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 0, 0, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: #fff;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.live-dot {
    width: 10px;
    height: 10px;
    background: #ff0000;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.9);
        box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
    }
    70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(255, 0, 0, 0);
    }
    100% {
        transform: scale(0.9);
        box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
    }
}

.sweepstakes-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.sweepstakes-stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    background: linear-gradient(to right, var(--ouro-antigo), #f4d03f);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-text {
    display: block;
    font-size: 0.85rem;
    color: rgba(245, 243, 238, 0.8);
    margin-top: 0.2rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.sweepstakes-timer {
    text-align: center;
    background: rgba(0, 0, 0, 0.2);
    padding: 1rem;
    border-radius: var(--radius-md);
}

.timer-label {
    display: block;
    font-size: 0.85rem;
    color: rgba(245, 243, 238, 0.8);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.timer {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.timer-unit {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: var(--carvao-noturno);
    border-radius: var(--radius-sm);
    padding: 8px 12px;
    min-width: 60px;
}

.timer-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ouro-antigo);
}

.timer-text {
    font-size: 0.8rem;
    color: rgba(245, 243, 238, 0.6);
    margin-top: -5px;
}

.btn-sweepstakes {
    width: 100%;
    background: var(--gradient-gold);
    border: none;
    color: var(--carvao-noturno);
    padding: 15px;
    border-radius: var(--radius-md);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    margin-bottom: 1.5rem;
}

.btn-sweepstakes:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
}

.sweepstakes-participants {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.participants-avatars {
    display: flex;
    align-items: center;
}

.participant-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--gradient-gold);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--carvao-noturno);
    font-size: 0.8rem;
    margin-left: -10px;
    border: 2px solid var(--carvao-noturno);
}

.participant-avatar:first-child {
    margin-left: 0;
}

.participant-count {
    margin-left: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--ouro-antigo);
}

.participants-message {
    font-size: 0.85rem;
    color: rgba(245, 243, 238, 0.7);
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: linear-gradient(135deg, #262626 0%, #1a1a1a 100%);
    position: relative;
    overflow: hidden;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.section-header.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.section-title {
    font-family: var(--font-titles);
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 700;
    color: var(--marmore-ivory);
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
    letter-spacing: 2px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--gradient-gold);
    background-size: 200% 200%;
    animation: goldShine 3s ease infinite;
    border-radius: 3px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: rgba(245, 243, 238, 0.8);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
    margin-top: 1.5rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2.5rem;
}

.feature-card {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    padding: 2.5rem 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-sm);
    transition: all 0.4s ease;
    transform: translateY(40px);
    opacity: 0;
}

.feature-card.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-card);
    border-color: rgba(212, 175, 55, 0.3);
}

.feature-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: var(--gradient-gold);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: var(--carvao-noturno);
    box-shadow: 0 10px 20px rgba(212, 175, 55, 0.3);
}

.feature-title {
    font-family: var(--font-titles);
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--marmore-ivory);
    margin-bottom: 1rem;
    position: relative;
    letter-spacing: 1px;
}

.feature-description {
    font-size: 0.95rem;
    color: rgba(245, 243, 238, 0.8);
    line-height: 1.7;
}

/* Prizes Section */
.prizes {
    padding: 6rem 0;
    background: linear-gradient(135deg, #1a1a1a 0%, #262626 100%);
    position: relative;
}

.prizes .section-title::after {
    background: var(--gradient-maroon);
}

.prizes .section-subtitle {
    margin-bottom: 1rem;
}

.prizes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem;
}

.prize-card {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    padding: 2.5rem 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-sm);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    transform: translateY(40px);
    opacity: 0;
}

.prize-card.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.prize-card.featured {
    border-color: var(--ouro-antigo);
    transform: scale(1.05);
    box-shadow: var(--shadow-card);
    z-index: 1;
}

.prize-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-card);
}

.prize-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.prize-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.prize-title {
    font-family: var(--font-titles);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--marmore-ivory);
    background: linear-gradient(135deg, var(--marmore-ivory) 0%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1px;
}

.prize-badge {
    background: var(--chronos-maroon);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.prize-amount {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    margin-bottom: 1.5rem;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.prize-features {
    list-style: none;
    margin-bottom: 2rem;
}

.prize-features li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
    font-size: 0.95rem;
    color: rgba(245, 243, 238, 0.9);
}

.prize-features i {
    color: var(--verde-sucesso);
    font-size: 0.9rem;
}

.btn-prize {
    width: 100%;
    background: var(--gradient-gold);
    border: none;
    color: var(--carvao-noturno);
    padding: 12px;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
}

.btn-prize:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

/* Transparency Section */
.transparency {
    padding: 6rem 0;
    background: var(--gradient-hero);
    position: relative;
}

.transparency-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 2.5rem;
    margin-bottom: 4rem;
}

.transparency-card {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    padding: 2.5rem 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-sm);
    transition: all 0.4s ease;
    transform: translateY(40px);
    opacity: 0;
}

.transparency-card.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.transparency-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-card);
    border-color: rgba(212, 175, 55, 0.3);
}

.transparency-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: var(--gradient-gold);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: var(--carvao-noturno);
    box-shadow: 0 10px 20px rgba(212, 175, 55, 0.3);
}

.transparency-card h3 {
    font-family: var(--font-titles);
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--marmore-ivory);
    margin-bottom: 1rem;
    position: relative;
    letter-spacing: 1px;
}

.transparency-card p {
    font-size: 0.95rem;
    color: rgba(245, 243, 238, 0.8);
    line-height: 1.7;
}

.blockchain-verification {
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-lg);
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    max-width: 800px;
    margin: 0 auto;
    transform: translateY(40px);
    opacity: 0;
    transition: all 0.4s ease;
}

.blockchain-verification.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.blockchain-verification h3 {
    font-family: var(--font-titles);
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--marmore-ivory);
    margin-bottom: 1.5rem;
    letter-spacing: 1px;
}

.blockchain-hash {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-md);
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.blockchain-hash code {
    font-family: monospace;
    font-size: 0.85rem;
    color: var(--ouro-antigo);
    word-break: break-all;
}

.btn-verify {
    background: var(--gradient-gold);
    border: none;
    color: var(--carvao-noturno);
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.btn-verify:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-button);
}

/* Testimonials */
.testimonials {
    padding: 6rem 0;
    background: linear-gradient(135deg, #262626 0%, #1a1a1a 100%);
    position: relative;
}

.testimonials .section-title::after {
    background: var(--gradient-gold);
}

.testimonials .section-subtitle {
    margin-bottom: 3rem;
}

.testimonials-slider {
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2.5rem;
}

.testimonial-card {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-sm);
    transition: all 0.4s ease;
    transform: translateY(40px);
    opacity: 0;
}

.testimonial-card.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-card);
    border-color: rgba(212, 175, 55, 0.3);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.stars {
    margin-bottom: 1rem;
    color: var(--ouro-antigo);
}

.stars i {
    margin-right: 3px;
}

.testimonial-content p {
    font-size: 1rem;
    color: rgba(245, 243, 238, 0.9);
    line-height: 1.7;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-gold);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--carvao-noturno);
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
}

.author-info h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--marmore-ivory);
    margin-bottom: 4px;
}

.author-info span {
    font-size: 0.85rem;
    color: rgba(245, 243, 238, 0.7);
}

.testimonials-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 3rem;
    gap: 2rem;
}

.nav-prev,
.nav-next {
    background: transparent;
    border: none;
    color: var(--marmore-ivory);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.nav-prev:hover,
.nav-next:hover {
    color: var(--ouro-antigo);
    opacity: 1;
}

.nav-dots {
    display: flex;
    gap: 8px;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: var(--ouro-antigo);
    width: 24px;
    border-radius: 10px;
}

/* Stats Section */
.stats-section {
    padding: 5rem 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><rect width="100" height="100" fill="%232F2F2F"/><path d="M0 0L100 100" stroke="%23D4AF37" stroke-width="1" stroke-opacity="0.1"/><path d="M0 100L100 0" stroke="%23D4AF37" stroke-width="1" stroke-opacity="0.1"/></svg>');
    background-size: 20px;
    position: relative;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(47, 47, 47, 0.95), rgba(47, 47, 47, 0.8));
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    position: relative;
    z-index: 1;
}

.stat-block {
    text-align: center;
    transform: translateY(40px);
    opacity: 0;
    transition: all 0.5s ease;
}

.stat-block.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.stat-number {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    color: var(--marmore-ivory);
    margin-bottom: 0.5rem;
    display: inline-block;
}

.stat-percent, 
.stat-plus {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 800;
    color: var(--ouro-antigo);
    display: inline-block;
    vertical-align: top;
    margin-left: 5px;
}

.stat-description {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(245, 243, 238, 0.8);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Final CTA Section */
.final-cta {
    padding: 6rem 0;
    background: var(--gradient-hero);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.final-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23D4AF37" opacity="0.1"/></svg>') repeat;
    background-size: 30px;
    opacity: 0.5;
    animation: float 20s infinite linear;
    z-index: 0;
}

@keyframes float {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(-100px);
    }
}

.cta-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
    transform: translateY(40px);
    opacity: 0;
    transition: all 0.5s ease;
}

.cta-content.animate-in {
    transform: translateY(0);
    opacity: 1;
}

.cta-content h2 {
    font-family: var(--font-titles);
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 700;
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
    letter-spacing: 2px;
}

.cta-content h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: var(--gradient-gold);
    border-radius: 3px;
}

.cta-content > p {
    font-size: 1.3rem;
    color: var(--marmore-ivory);
    margin-bottom: 3rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.cta-benefits {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.benefit {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(212, 175, 55, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 50px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    color: var(--marmore-ivory);
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.benefit:hover {
    background: rgba(212, 175, 55, 0.2);
    border-color: var(--ouro-antigo);
    transform: translateY(-5px);
}

.benefit i {
    color: var(--ouro-antigo);
    font-size: 1.2rem;
}

.btn-final-cta {
    background: var(--gradient-gold);
    border: none;
    color: var(--carvao-noturno);
    padding: 18px 36px;
    border-radius: var(--radius-md);
    font-size: clamp(1.1rem, 3vw, 1.3rem);
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-button);
    display: inline-flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 1.5rem;
}

.btn-final-cta:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(212, 175, 55, 0.5);
}

.cta-disclaimer {
    color: rgba(245, 243, 238, 0.7);
    font-size: 0.9rem;
    max-width: 500px;
    margin: 0 auto;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: var(--marmore-ivory);
    padding: 5rem 0 2rem;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--ouro-antigo), transparent);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h4 {
    font-family: var(--font-titles);
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--ouro-antigo);
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
    letter-spacing: 1px;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--ouro-antigo);
    border-radius: 2px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 1.5rem;
}

.footer-logo-img {
    height: 40px;
    width: auto;
}

.footer-logo-text {
    font-family: var(--font-titles);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ouro-antigo);
    letter-spacing: 3px;
    text-transform: uppercase;
}

.footer-description {
    font-size: 0.95rem;
    color: rgba(245, 243, 238, 0.7);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer-social {
    display: flex;
    gap: 15px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--marmore-ivory);
    border-radius: 50%;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.social-link:hover {
    background: var(--gradient-gold);
    color: var(--carvao-noturno);
    transform: translateY(-5px);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: rgba(245, 243, 238, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    position: relative;
}

.footer-links a:hover {
    color: var(--ouro-antigo);
    padding-left: 5px;
}

.footer-bottom {
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
}

.footer-bottom p {
    color: rgba(245, 243, 238, 0.5);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.footer-disclaimer {
    color: rgba(245, 243, 238, 0.5);
    font-size: 0.8rem;
}

/* Cookie Consent */
.cookie-consent {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100px);
    width: calc(100% - 40px);
    max-width: 1000px;
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-lg);
    z-index: 90;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    opacity: 0;
    transition: all 0.5s ease;
}

.cookie-consent.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.cookie-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.cookie-content p {
    flex: 1;
    min-width: 200px;
    font-size: 0.95rem;
    color: rgba(245, 243, 238, 0.9);
}

.cookie-content a {
    color: var(--ouro-antigo);
    text-decoration: none;
}

.cookie-content a:hover {
    text-decoration: underline;
}

.cookie-buttons {
    display: flex;
    gap: 10px;
}

.btn-cookie-accept {
    background: var(--gradient-gold);
    border: none;
    color: var(--carvao-noturno);
    padding: 10px 20px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cookie-settings {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--marmore-ivory);
    padding: 10px 20px;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cookie-accept:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-button);
}

.btn-cookie-settings:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
}

.modal {
    background: var(--gradient-card);
    border-radius: var(--radius-lg);
    width: 90%;
    max-width: 500px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    position: relative;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.4s ease;
    pointer-events: all;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.modal.show {
    opacity: 1;
    transform: translateY(0);
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: transparent;
    border: none;
    color: var(--marmore-ivory);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-close:hover {
    color: var(--ouro-antigo);
    transform: rotate(90deg);
}

.modal-header {
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    font-family: var(--font-titles);
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--marmore-ivory);
    margin-bottom: 0.5rem;
    letter-spacing: 1px;
}

.modal-header p {
    font-size: 0.95rem;
    color: rgba(245, 243, 238, 0.7);
}

.modal-body {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--marmore-ivory);
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    padding: 12px 15px;
    font-size: 1rem;
    color: var(--marmore-ivory);
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--ouro-antigo);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.btn-modal-submit {
    background: var(--gradient-gold);
    border: none;
    color: var(--carvao-noturno);
    padding: 12px 25px;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-modal-submit:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-button);
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    background: var(--gradient-card);
    color: var(--marmore-ivory);
    text-align: center;
    border-radius: var(--radius-md);
    padding: 10px 15px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    width: 200px;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    pointer-events: none;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--carvao-noturno) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 1s ease forwards;
}

.fade-in-up {
    animation: fadeInUp 1s ease forwards;
}

.fade-in-down {
    animation: fadeInDown 1s ease forwards;
}

.fade-in-left {
    animation: fadeInLeft 1s ease forwards;
}

.fade-in-right {
    animation: fadeInRight 1s ease forwards;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hero-stats {
        flex-wrap: wrap;
    }
    
    .stat-card {
        width: 160px;
    }
    
    .hero-cta {
        flex-wrap: wrap;
    }
    
    .footer-content {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: 0;
        right: 0;
        background: rgba(47, 47, 47, 0.95);
        backdrop-filter: blur(10px);
        flex-direction: column;
        padding: 2rem;
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.4s ease;
        z-index: 999;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .hero-cta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn-hero-primary,
    .btn-hero-secondary {
        width: 100%;
        justify-content: center;
    }
    
    .sweepstakes-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .sweepstakes-info {
        flex-direction: column;
    }
    
    .timer {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .features-grid,
    .prizes-grid,
    .transparency-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .prize-card.featured {
        transform: scale(1);
    }
    
    .prize-card.featured:hover {
        transform: translateY(-10px);
    }
    
    .cta-benefits {
        flex-direction: column;
    }
    
    .benefit {
        width: 100%;
        justify-content: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2.5rem;
    }
    
    .cookie-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .cookie-buttons {
        width: 100%;
        justify-content: space-between;
    }
}

@media (max-width: 480px) {
    .hero-stats {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        width: 100%;
    }
    
    .sweepstakes-card {
        padding: 1.5rem;
    }
    
    .feature-card,
    .prize-card,
    .transparency-card,
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .cta-content h2 {
        font-size: 2rem;
    }
    
    .btn-final-cta {
        width: 100%;
        padding: 15px;
    }
    
    .blockchain-hash {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .btn-verify {
        width: 100%;
        justify-content: center;
    }
}

/* Accessibility */
.screen-reader-text {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

:focus {
    outline: 2px solid var(--ouro-antigo);
}

:focus:not(:focus-visible) {
    outline: none;
}

:focus-visible {
    outline: 2px solid var(--ouro-antigo);
}

/* Adicionar uma pequena animação de brilho para os textos dourados */
@keyframes goldShine {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
